package linetv

import (
	"net/http"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model/playback"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/manifest"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/go-zoo/bone"
)

type Handler struct {
	manifestLegacyHelper manifest.LegacyHelper
	playbackLegacyHelper playback.LegacyHelper
	clock                clock.Clock
}

func NewHandler() *Handler {
	metaCacheReader := cache.New(container.CachePoolMeta().Slave())
	return &Handler{
		manifestLegacyHelper: manifest.NewLegacyHelper(
			clock.New(),
			config.TheaterCDNSignKey,
			metaCacheReader,
		),
		playbackLegacyHelper: playback.NewLegacyHelper(),
		clock:                clock.New(),
	}
}

func (h *Handler) RequireStreamingManifest(w http.ResponseWriter, r *http.Request) {
	episodeID := bone.GetValue(r, "episodeID")
	if episodeID == "" || len(episodeID) != 14 {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	}

	deviceID := r.Header.Get(httpreq.HeaderDeviceID)
	if deviceID == "" {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	}

	req := PostEpisodeManifestReq{
		Purpose: "playback",
	}
	if err := httpreq.PayloadBinding(&req, r); err != nil {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	}

	titleID := episodeID[:8]
	targetFile := req.Quality

	manifests, err := h.manifestLegacyHelper.GetManifestsByTargetFile([]string{episodeID}, targetFile, req.Subtitles)
	if err != nil {
		plog.Error("linetvHandler: RequireStreamingManifest: failed to get manifests").
			Err(err).
			Str("episode_id", episodeID).
			Str("target_file", targetFile).
			Bool("subtitles", req.Subtitles).
			Send()
		render.JSONInternalServerErr(w, ErrUnknown)
		return
	}
	if len(manifests) == 0 {
		render.JSONNotFound(w, ErrEpisodeNotFound)
		return
	}

	mf := manifests[0]

	playbackRequest := playback.PlaybackRequest{
		TitleID:   titleID,
		EpisodeID: episodeID,
		Medium:    "SVOD",
		Purpose:   req.Purpose,
	}

	playbackToken, err := h.playbackLegacyHelper.GetToken(req.UserID, deviceID, playbackRequest)
	if err != nil {
		plog.Error("linetvHandler: RequireStreamingManifest: failed to get playback token").
			Err(err).
			Str("user_id", req.UserID).
			Str("device_id", deviceID).
			Str("episode_id", episodeID).
			Send()
		render.JSONInternalServerErr(w, ErrUnknown)
		return
	}

	response := PostEpisodeManifestResp{
		StreamingAssets: StreamingAsset{
			DefaultSubtitle:  mf.DefaultSubtitle,
			SubtitleURL:      mf.SubtitleURL,
			ThumbnailURL:     mf.ThumbnailURL,
			SupportedQuality: mf.SupportedQuality,
			Dash: ManifestFile{
				URL:  mf.Dash.URL,
				Size: mf.Dash.Size,
			},
			Hls: ManifestFile{
				URL:  mf.Hls.URL,
				Size: mf.Hls.Size,
			},
		},
		LicenseURL: map[string]string{
			"playready":     config.KKSBVLicenseUrl,
			"widevine":      config.KKSBVLicenseUrl,
			"fairplay":      config.LicenseUrlFairplay,
			"fairplay_cert": config.LicenseUrlFairplay + "/fairplay_cert",
		},
		PlaybackToken: PlaybackToken{
			Token:     playbackToken.PlaybackToken,
			CreatedAt: datatype.DateTime(time.Unix(playbackToken.CreatedAt, 0)),
			ExpiresAt: datatype.DateTime(time.Unix(playbackToken.ExpiresAt, 0)),
		},
		LicenseHeaders: []LicenseHeader{
			{
				Key:   "X-KK-Tenant-Id",
				Value: config.KKSBVTenantIDForLineTV,
				For:   []string{"playready", "widevine"},
			},
		},
	}

	resp := rest.Ok()
	resp.Data = response
	render.JSON(w, http.StatusOK, resp)
}
